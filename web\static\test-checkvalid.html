<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CheckValid 函数测试</title>
    
    <!-- 本地CSS库 -->
    <link href="./libs/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="./libs/tabler/css/tabler.min.css" rel="stylesheet">
    <link href="./libs/nprogress.min.css" rel="stylesheet">
    
    <style>
        .test-container {
            max-width: 900px;
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .status-item {
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 0.375rem;
            border: 1px solid #dee2e6;
        }
        
        .status-success {
            background-color: #d1e7dd;
            color: #0f5132;
            border-color: #badbcc;
        }
        
        .status-error {
            background-color: #f8d7da;
            color: #842029;
            border-color: #f5c2c7;
        }
        
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-color: #b8daff;
        }
        
        .status-warning {
            background-color: #fff3cd;
            color: #664d03;
            border-color: #ffecb5;
        }
        
        .test-section {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CheckValid 函数测试</h1>
        <p>此页面用于测试修复后的 checkValid 函数是否正常工作</p>
        
        <!-- 测试控制 -->
        <div class="test-section">
            <h3>测试控制</h3>
            <div class="btn-group mb-3" role="group">
                <button type="button" class="btn btn-primary" onclick="testCheckValid()">测试 checkValid 函数</button>
                <button type="button" class="btn btn-secondary" onclick="testAxiosClient()">测试 axios_client</button>
                <button type="button" class="btn btn-info" onclick="testHealthEndpoint()">测试 /health 接口</button>
                <button type="button" class="btn btn-warning" onclick="clearLogs()">清空日志</button>
            </div>
        </div>
        
        <!-- 依赖检查 -->
        <div class="test-section">
            <h3>依赖检查</h3>
            <div id="dependency_status"></div>
        </div>
        
        <!-- 测试结果 -->
        <div class="test-section">
            <h3>测试日志</h3>
            <div id="test_logs" class="log-container"></div>
        </div>
        
        <!-- 函数状态 -->
        <div class="test-section">
            <h3>函数状态</h3>
            <div id="function_status"></div>
        </div>
    </div>

    <!-- JavaScript库 - 按照正确顺序加载 -->
    <script src="./js/jquery-3.3.1.min.js"></script>
    <script src="./libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="./libs/tabler/js/tabler.min.js"></script>
    <script src="./libs/navigo.min.js"></script>
    <script src="./libs/nprogress.min.js"></script>
    <script src="./js/axios.min.js"></script>
    <script src="./js/auth-manager.js"></script>
    <script src="./js/axios-wrapper.js"></script>
    <script src="./js/util.js"></script>
    <script src="./js/functions.js"></script>
    <script src="./js/app.js"></script>

    <script>
        // 测试日志
        let testLogs = [];
        
        // 添加日志
        function addLog(level, message, details = null) {
            const timestamp = new Date().toLocaleTimeString();
            const log = {
                timestamp,
                level,
                message,
                details
            };
            testLogs.push(log);
            updateLogDisplay();
        }
        
        // 更新日志显示
        function updateLogDisplay() {
            const logsContainer = document.getElementById('test_logs');
            logsContainer.innerHTML = '';
            
            testLogs.forEach(log => {
                const logElement = document.createElement('div');
                logElement.className = `mb-1`;
                
                let levelColor = 'text-muted';
                switch(log.level) {
                    case 'success': levelColor = 'text-success'; break;
                    case 'error': levelColor = 'text-danger'; break;
                    case 'warning': levelColor = 'text-warning'; break;
                    case 'info': levelColor = 'text-info'; break;
                }
                
                logElement.innerHTML = `
                    <span class="text-muted">[${log.timestamp}]</span>
                    <span class="${levelColor}">[${log.level.toUpperCase()}]</span>
                    <span>${log.message}</span>
                    ${log.details ? `<br><span class="text-muted ps-4">${JSON.stringify(log.details, null, 2)}</span>` : ''}
                `;
                
                logsContainer.appendChild(logElement);
            });
            
            // 滚动到底部
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        // 清空日志
        function clearLogs() {
            testLogs = [];
            updateLogDisplay();
        }
        
        // 检查依赖
        function checkDependencies() {
            const dependencies = [
                { name: 'jQuery', check: () => typeof $ !== 'undefined', required: true },
                { name: 'axios', check: () => typeof axios !== 'undefined', required: true },
                { name: 'window.axios_client', check: () => typeof window.axios_client !== 'undefined', required: true },
                { name: 'checkValid', check: () => typeof checkValid !== 'undefined', required: true },
                { name: 'Navigo', check: () => typeof Navigo !== 'undefined', required: false },
                { name: 'NProgress', check: () => typeof NProgress !== 'undefined', required: false }
            ];
            
            const statusContainer = document.getElementById('dependency_status');
            statusContainer.innerHTML = '';
            
            dependencies.forEach(dep => {
                const isAvailable = dep.check();
                const statusDiv = document.createElement('div');
                statusDiv.className = `status-item status-${isAvailable ? 'success' : (dep.required ? 'error' : 'warning')}`;
                statusDiv.innerHTML = `
                    <strong>${dep.name}:</strong> 
                    ${isAvailable ? '✅ 可用' : '❌ 不可用'}
                    ${dep.required ? ' (必需)' : ' (可选)'}
                `;
                statusContainer.appendChild(statusDiv);
                
                addLog(isAvailable ? 'success' : (dep.required ? 'error' : 'warning'), 
                      `依赖检查: ${dep.name} - ${isAvailable ? '可用' : '不可用'}`);
            });
        }
        
        // 测试 axios_client
        async function testAxiosClient() {
            addLog('info', '开始测试 axios_client...');
            
            if (!window.axios_client) {
                addLog('error', 'window.axios_client 不存在');
                return;
            }
            
            addLog('success', 'window.axios_client 存在');
            addLog('info', 'axios_client 配置', {
                baseURL: window.axios_client.defaults.baseURL,
                timeout: window.axios_client.defaults.timeout,
                withCredentials: window.axios_client.defaults.withCredentials
            });
        }
        
        // 测试 /health 接口
        async function testHealthEndpoint() {
            addLog('info', '开始测试 /health 接口...');
            
            if (!window.axios_client) {
                addLog('error', 'axios_client 不可用，无法测试接口');
                return;
            }
            
            try {
                const response = await window.axios_client.post('/health');
                addLog('success', '/health 接口调用成功', {
                    status: response.status,
                    data: response.data
                });
            } catch (error) {
                addLog('error', '/health 接口调用失败', {
                    status: error.response?.status,
                    message: error.message,
                    data: error.response?.data
                });
            }
        }
        
        // 测试 checkValid 函数
        async function testCheckValid() {
            addLog('info', '开始测试 checkValid 函数...');
            
            if (typeof checkValid !== 'function') {
                addLog('error', 'checkValid 函数不存在');
                return;
            }
            
            try {
                addLog('info', '调用 checkValid()...');
                const result = await checkValid();
                addLog('success', `checkValid 返回结果: ${result}`, { result });
                
                // 更新函数状态显示
                const statusContainer = document.getElementById('function_status');
                statusContainer.innerHTML = `
                    <div class="status-item status-${result ? 'success' : 'error'}">
                        <strong>checkValid() 结果:</strong> ${result ? '✅ 验证通过' : '❌ 验证失败'}
                    </div>
                `;
                
            } catch (error) {
                addLog('error', 'checkValid 函数执行出错', {
                    message: error.message,
                    stack: error.stack
                });
                
                const statusContainer = document.getElementById('function_status');
                statusContainer.innerHTML = `
                    <div class="status-item status-error">
                        <strong>checkValid() 错误:</strong> ${error.message}
                    </div>
                `;
            }
        }
        
        // 页面加载完成后自动检查
        $(document).ready(function() {
            addLog('info', '页面加载完成，开始检查依赖...');
            checkDependencies();
            
            // 延迟一秒后自动测试
            setTimeout(() => {
                addLog('info', '自动开始测试...');
                testAxiosClient();
            }, 1000);
        });
    </script>
</body>
</html>
